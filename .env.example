# Application
PORT=3000
NODE_ENV=development

# OpenAI/LM Studio Configuration
OPENAI_API_KEY=lm-studio
OPENAI_BASE_URL=http://127.0.0.1:1234/v1
OPENAI_MODEL=devstral-small-2505-mlx

# PostgreSQL Database
DATABASE_URL=postgresql://postgres:<EMAIL>:5433/sw_dev
DATABASE_HOST=itrdev.lan
DATABASE_PORT=5433
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=sw_dev

# MCP Configuration
MCP_ENABLED=true

# Context Configuration
CONTEXT_INCLUDE_STATIC_DESCRIPTION=true
CONTEXT_STATIC_DESCRIPTION="Event teams are located in roster_team table. Athlete names can be retrieved using master_athlete_id in master_athlete table. Competition results are stored in results table with references to athletes and events."
