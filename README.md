# SW Search Agent

A sophisticated search agent that enables natural language querying of PostgreSQL databases using AI. Built with TypeScript, NestJS, and integrating OpenAI/LM Studio with Model Context Protocol (MCP) for secure database access.

## 🏗️ Architecture Overview

The application implements a **rolling messages array pattern** for AI workflows, where:
- System prompts initialize the conversation context
- User queries are added to the message history
- AI assistant responses and tool calls are tracked
- Database tool results are injected back as tool messages
- The conversation maintains context across multiple interactions

### Core Workflow
```
User Query → AI Assistant → Tool Calls → MCP Database → Tool Results → Final Response
     ↓                                                                        ↑
Message Array: [system, user, assistant, tool, assistant, tool, ...]
```

## 🛠️ Technology Stack

### Backend
- **Framework**: NestJS (Node.js)
- **Language**: TypeScript
- **AI Integration**: OpenAI SDK (compatible with LM Studio)
- **Database**: PostgreSQL (read-only access)
- **Protocol**: Model Context Protocol (MCP) for database tools
- **Configuration**: Environment-based with @nestjs/config

### Frontend
- **UI**: Vanilla HTML/CSS/JavaScript
- **Styling**: Modern CSS with responsive design
- **Communication**: REST API with fetch()

### Development Tools
- **Build**: TypeScript compiler with NestJS CLI
- **Code Quality**: ESLint + Prettier
- **Process Management**: npm scripts with hot reload

## 📁 Project Structure

```
sw-search-agent/
├── src/
│   ├── main.ts                 # Application entry point
│   ├── app.module.ts           # Root module with all imports
│   ├── config/
│   │   └── configuration.ts    # Environment configuration loader
│   ├── auth/                   # Authentication module (placeholder)
│   │   └── auth.module.ts      # Future auth implementation
│   ├── openai/                 # OpenAI/LM Studio integration
│   │   ├── openai.module.ts    # OpenAI module definition
│   │   └── openai.service.ts   # Chat completion service
│   ├── mcp/                    # Model Context Protocol implementation
│   │   ├── mcp.module.ts       # MCP module definition
│   │   └── mcp.service.ts      # PostgreSQL MCP tools
│   └── search/                 # Main search functionality
│       ├── search.module.ts    # Search module definition
│       ├── search.controller.ts # REST API endpoints
│       └── search.service.ts   # Core search logic
├── public/                     # Static web assets
│   ├── index.html             # Main UI page
│   ├── style.css              # Application styling
│   └── script.js              # Frontend JavaScript
├── .env                       # Environment variables
├── .env.example              # Environment template
├── package.json              # Dependencies and scripts
├── tsconfig.json             # TypeScript configuration
├── .eslintrc.js              # ESLint configuration
├── .prettierrc               # Prettier configuration
└── nest-cli.json             # NestJS CLI configuration
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Application
PORT=3000
NODE_ENV=development

# OpenAI/LM Studio Configuration
OPENAI_API_KEY=lm-studio
OPENAI_BASE_URL=http://127.0.0.1:1234/v1
OPENAI_MODEL=devstral-small-2505-mlx

# PostgreSQL Database
DATABASE_URL=postgresql://postgres:<EMAIL>:5433/sw_dev
DATABASE_HOST=itrdev.lan
DATABASE_PORT=5433
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=sw_dev

# MCP Configuration
MCP_ENABLED=true
```

## 🗄️ Database Integration

### MCP Tools Available
The application provides three core database tools via MCP:

1. **`list_tables`**: Lists all tables in the public schema
2. **`describe_table`**: Returns column information for a specific table
3. **`query_database`**: Executes read-only SELECT queries

### Security Features
- **Read-only access**: Only SELECT statements are permitted
- **Query validation**: Non-SELECT queries are rejected
- **Error handling**: Database errors are caught and returned safely
- **Connection pooling**: Single persistent connection with proper cleanup

## 🌐 API Endpoints

### REST API
- `POST /api/search` - Main search endpoint
  - Body: `{ query: string, conversationId?: string }`
  - Returns: `{ response: string, conversationId: string, toolCalls: array }`

- `GET /api/search/conversation/:id` - Retrieve conversation history
- `DELETE /api/search/conversation/:id` - Clear conversation

### Static Assets
- `GET /` - Web UI (served from public/)
- `GET /style.css` - Application styles
- `GET /script.js` - Frontend JavaScript

## 🚀 Available Scripts

```bash
npm run dev      # Start development server with hot reload
npm run start    # Start production server
npm run build    # Build the application
npm run lint     # Run ESLint
npm run format   # Format code with Prettier
```

## 📊 Logging

The application implements comprehensive logging across all modules:

- **Request/Response tracking** with timing metrics
- **Database operation logging** with query performance
- **AI interaction logging** with token usage
- **Tool execution tracking** with success/failure status
- **Error logging** with full stack traces

Log levels: `LOG`, `ERROR`, `WARN` with contextual information.

## 🔄 Message Flow Architecture

### Conversation Management
- **Conversation ID**: Unique identifier for each user session
- **Message Persistence**: In-memory storage of conversation history
- **Context Preservation**: System prompts and conversation state maintained

### Tool Integration Pattern
```typescript
// Rolling message array pattern
messages = [
  { role: 'system', content: 'System prompt...' },
  { role: 'user', content: 'User query' },
  { role: 'assistant', content: 'Response', tool_calls: [...] },
  { role: 'tool', content: 'Tool result', tool_call_id: 'id' },
  { role: 'assistant', content: 'Final response' }
]
```

## 🛡️ Security Considerations

- **Database Access**: Read-only PostgreSQL access with query validation
- **Environment Variables**: Sensitive configuration externalized
- **CORS**: Enabled for frontend communication
- **Error Handling**: Safe error messages without sensitive data exposure
- **Input Validation**: Query sanitization and type checking

## 🎯 Use Cases

- **Database Exploration**: "Show me all tables in the database"
- **Schema Analysis**: "What's the structure of the users table?"
- **Data Querying**: "Find all products with price greater than 100"
- **Analytics**: "How many records are in each table?"
- **Relationship Discovery**: "Show me the foreign key relationships"

## 🔮 Future Enhancements

- **Authentication**: User management and access control
- **Query Caching**: Redis integration for performance
- **Advanced MCP Tools**: Write operations, stored procedures
- **Multi-database Support**: Multiple PostgreSQL instances
- **Export Functionality**: CSV, JSON data export
- **Query History**: Persistent conversation storage
