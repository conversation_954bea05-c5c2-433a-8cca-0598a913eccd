import { Injectable, Logger } from '@nestjs/common';
import { OpenaiService, ChatMessage } from '../openai/openai.service';
import { McpService, McpToolCall } from '../mcp/mcp.service';
import { ContextService } from '../context/context.service';

export interface SearchRequest {
  query: string;
  conversationId?: string;
}

export interface SearchResponse {
  response: string;
  conversationId: string;
  toolCalls?: any[];
}

@Injectable()
export class SearchService {
  private readonly logger = new Logger(SearchService.name);
  private conversations = new Map<string, ChatMessage[]>();

  constructor(
    private openaiService: OpenaiService,
    private mcpService: McpService,
    private contextService: ContextService,
  ) {}

  async search(request: SearchRequest): Promise<SearchResponse> {
    this.logger.log(`Starting search request: ${JSON.stringify({ query: request.query, conversationId: request.conversationId })}`);

    const conversationId = request.conversationId || this.generateConversationId();
    this.logger.log(`Using conversation ID: ${conversationId}`);

    // Get or initialize conversation
    let messages = this.conversations.get(conversationId) || [];
    this.logger.log(`Found ${messages.length} existing messages in conversation`);

    // Initialize with system message if this is a new conversation
    if (messages.length === 0) {
      this.logger.log('Initializing new conversation with enhanced system message');
      const systemPrompt = await this.contextService.getSystemPromptWithContext();
      messages.push({
        role: 'system',
        content: systemPrompt,
      });
    }

    // Add user message
    this.logger.log(`Adding user message: ${request.query}`);
    messages.push({
      role: 'user',
      content: request.query,
    });

    // Get available tools from MCP service
    this.logger.log('Fetching available tools from MCP service');
    const availableTools = await this.mcpService.getAvailableTools();
    this.logger.log(`Found ${availableTools.length} available tools: ${availableTools.map(t => t.name).join(', ')}`);

    // Convert MCP tools to OpenAI format
    const openaiTools = availableTools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.inputSchema,
      },
    }));

    let finalResponse = '';
    let toolCalls = [];
    let iterationCount = 0;

    // Main conversation loop
    this.logger.log('Starting conversation loop with OpenAI');
    while (true) {
      iterationCount++;
      this.logger.log(`Conversation iteration ${iterationCount}`);

      this.logger.log(`Sending ${messages.length} messages to OpenAI`);
      const completion = await this.openaiService.createChatCompletion(
        messages,
        openaiTools,
        'auto',
      );

      const assistantMessage = completion.choices[0].message;
      this.logger.log(`Received assistant response: ${assistantMessage.content?.substring(0, 100)}...`);

      // Add assistant message to conversation
      messages.push({
        role: 'assistant',
        content: assistantMessage.content || '',
        tool_calls: assistantMessage.tool_calls,
      });

      // Check if assistant wants to use tools
      if (assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {
        this.logger.log(`Assistant requested ${assistantMessage.tool_calls.length} tool calls`);

        // Execute each tool call
        for (const toolCall of assistantMessage.tool_calls) {
          this.logger.log(`Executing tool: ${toolCall.function.name} with args: ${toolCall.function.arguments}`);
          toolCalls.push(toolCall);

          const mcpToolCall: McpToolCall = {
            name: toolCall.function.name,
            arguments: JSON.parse(toolCall.function.arguments),
          };

          const toolResult = await this.mcpService.executeTool(mcpToolCall);
          this.logger.log(`Tool result: ${toolResult.isError ? 'ERROR' : 'SUCCESS'}`);

          // Add tool result to conversation
          messages.push({
            role: 'tool',
            content: toolResult.content[0]?.text || 'No result',
            tool_call_id: toolCall.id,
          });
        }

        // Continue the conversation to get the final response
        this.logger.log('Continuing conversation for final response');
        continue;
      } else {
        // No more tool calls, this is the final response
        finalResponse = assistantMessage.content || '';
        this.logger.log(`Final response received: ${finalResponse.substring(0, 100)}...`);
        break;
      }
    }

    // Store updated conversation
    this.conversations.set(conversationId, messages);
    this.logger.log(`Conversation stored with ${messages.length} total messages`);

    const result = {
      response: finalResponse,
      conversationId,
      toolCalls,
    };

    this.logger.log(`Search completed successfully. Tool calls: ${toolCalls.length}, Response length: ${finalResponse.length}`);
    return result;
  }

  private generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getConversation(conversationId: string): ChatMessage[] {
    return this.conversations.get(conversationId) || [];
  }

  clearConversation(conversationId: string): boolean {
    return this.conversations.delete(conversationId);
  }
}
