export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',

  openai: {
    apiKey: process.env.OPENAI_API_KEY || 'lm-studio',
    baseURL: process.env.OPENAI_BASE_URL || 'http://127.0.0.1:1234/v1',
    model: process.env.OPENAI_MODEL || 'devstral-small-2505-mlx',
  },

  database: {
    url: process.env.DATABASE_URL || 'postgresql://postgres:<EMAIL>:5433/sw_dev',
    host: process.env.DATABASE_HOST || 'itrdev.lan',
    port: parseInt(process.env.DATABASE_PORT, 10) || 5433,
    username: process.env.DATABASE_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || 'postgres',
    name: process.env.DATABASE_NAME || 'sw_dev',
  },

  mcp: {
    enabled: process.env.MCP_ENABLED !== 'false', // Default to true
    servers: [
      {
        name: 'postgres',
        type: 'local',
        command: 'npx',
        args: [
          '@modelcontextprotocol/server-postgres',
          process.env.DATABASE_URL || 'postgresql://postgres:<EMAIL>:5433/sw_dev'
        ],
        env: {},
      },
      // Future servers can be added here
    ],
  },

  context: {
    includeSchema: process.env.CONTEXT_INCLUDE_SCHEMA !== 'false', // Default to true
    maxTables: parseInt(process.env.CONTEXT_MAX_TABLES, 10) || 50,
    includeConstraints: process.env.CONTEXT_INCLUDE_CONSTRAINTS !== 'false', // Default to true
    includeSampleData: process.env.CONTEXT_INCLUDE_SAMPLE_DATA === 'true', // Default to false
    sampleDataLimit: parseInt(process.env.CONTEXT_SAMPLE_DATA_LIMIT, 10) || 5,
  },
});
