import { Injectable, Lo<PERSON>, On<PERSON><PERSON>ule<PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from 'pg';

export interface McpTool {
  name: string;
  description: string;
  inputSchema: any;
}

export interface McpToolCall {
  name: string;
  arguments: any;
}

export interface McpToolResult {
  content: any[];
  isError?: boolean;
}

@Injectable()
export class McpService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(McpService.name);
  private pgClient: Client;

  constructor(private configService: ConfigService) { }

  public async onModuleInit() {
    await this.initializeDatabase();
    this.logger.log('MCP Service initialized successfully');
  }

  async onModuleDestroy() {
    if (this.pgClient) {
      await this.pgClient.end();
      this.logger.log('Disconnected from PostgreSQL database');
    }
  }

  private async initializeDatabase() {
    const dbConfig = this.configService.get('database');

    this.logger.log(`Initializing PostgreSQL connection to ${dbConfig.host}:${dbConfig.port}/${dbConfig.name}`);

    this.pgClient = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.username,
      password: dbConfig.password,
      database: dbConfig.name,
    });

    try {
      const startTime = Date.now();
      await this.pgClient.connect();
      const duration = Date.now() - startTime;
      this.logger.log(`Connected to PostgreSQL database in ${duration}ms`);

      // Test the connection
      const result = await this.pgClient.query('SELECT version()');
      this.logger.log(`Database version: ${result.rows[0].version.substring(0, 50)}...`);
    } catch (error) {
      this.logger.error(`Failed to connect to PostgreSQL: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getAvailableTools(): Promise<McpTool[]> {
    this.logger.log('Providing available MCP tools');

    const tools = [
      {
        name: 'query_database',
        description: 'Execute a read-only SQL query against the PostgreSQL database',
        inputSchema: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'The SQL query to execute (SELECT statements only)',
            },
          },
          required: ['query'],
        },
      },
      {
        name: 'describe_table',
        description: 'Get the schema information for a specific table',
        inputSchema: {
          type: 'object',
          properties: {
            tableName: {
              type: 'string',
              description: 'The name of the table to describe',
            },
          },
          required: ['tableName'],
        },
      },
      {
        name: 'list_tables',
        description: 'List all tables in the database',
        inputSchema: {
          type: 'object',
          properties: {},
        },
      },
    ];

    this.logger.log(`Returning ${tools.length} available tools: ${tools.map(t => t.name).join(', ')}`);
    return tools;
  }

  async executeTool(toolCall: McpToolCall): Promise<McpToolResult> {
    this.logger.log(`Executing tool: ${toolCall.name} with arguments: ${JSON.stringify(toolCall.arguments)}`);

    try {
      const startTime = Date.now();
      let result: McpToolResult;

      switch (toolCall.name) {
        case 'query_database':
          result = await this.queryDatabase(toolCall.arguments.query);
          break;

        case 'describe_table':
          result = await this.describeTable(toolCall.arguments.tableName);
          break;

        case 'list_tables':
          result = await this.listTables();
          break;

        default:
          this.logger.warn(`Unknown tool requested: ${toolCall.name}`);
          result = {
            content: [{ type: 'text', text: `Unknown tool: ${toolCall.name}` }],
            isError: true,
          };
      }

      const duration = Date.now() - startTime;
      this.logger.log(`Tool ${toolCall.name} executed in ${duration}ms. Success: ${!result.isError}`);

      return result;
    } catch (error) {
      this.logger.error(`Error executing tool ${toolCall.name}: ${error.message}`, error.stack);
      return {
        content: [{ type: 'text', text: `Error: ${error.message}` }],
        isError: true,
      };
    }
  }

  private async queryDatabase(query: string): Promise<McpToolResult> {
    this.logger.log(`Executing database query: ${query.substring(0, 100)}...`);

    // Ensure only SELECT queries are allowed
    const trimmedQuery = query.trim().toLowerCase();
    if (!trimmedQuery.startsWith('select')) {
      this.logger.warn(`Rejected non-SELECT query: ${query.substring(0, 50)}...`);
      return {
        content: [{ type: 'text', text: 'Only SELECT queries are allowed' }],
        isError: true,
      };
    }

    try {
      const startTime = Date.now();
      const result = await this.pgClient.query(query);
      const duration = Date.now() - startTime;

      this.logger.log(`Query executed successfully in ${duration}ms. Returned ${result.rowCount} rows`);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              rows: result.rows,
              rowCount: result.rowCount,
              fields: result.fields.map(f => ({ name: f.name, dataTypeID: f.dataTypeID })),
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error(`Query execution failed: ${error.message}`);
      return {
        content: [{ type: 'text', text: `Query error: ${error.message}` }],
        isError: true,
      };
    }
  }

  private async describeTable(tableName: string): Promise<McpToolResult> {
    this.logger.log(`Describing table: ${tableName}`);

    const query = `
      SELECT
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length
      FROM information_schema.columns
      WHERE table_name = $1
      ORDER BY ordinal_position;
    `;

    try {
      const startTime = Date.now();
      const result = await this.pgClient.query(query, [tableName]);
      const duration = Date.now() - startTime;

      this.logger.log(`Table ${tableName} described successfully in ${duration}ms. Found ${result.rowCount} columns`);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              table: tableName,
              columns: result.rows,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error(`Error describing table ${tableName}: ${error.message}`);
      return {
        content: [{ type: 'text', text: `Error describing table: ${error.message}` }],
        isError: true,
      };
    }
  }

  private async listTables(): Promise<McpToolResult> {
    this.logger.log('Listing all tables in database');

    const query = `
      SELECT table_name, table_type
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;

    try {
      const startTime = Date.now();
      const result = await this.pgClient.query(query);
      const duration = Date.now() - startTime;

      this.logger.log(`Tables listed successfully in ${duration}ms. Found ${result.rowCount} tables`);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              tables: result.rows,
            }, null, 2),
          },
        ],
      };
    } catch (error) {
      this.logger.error(`Error listing tables: ${error.message}`);
      return {
        content: [{ type: 'text', text: `Error listing tables: ${error.message}` }],
        isError: true,
      };
    }
  }
}
