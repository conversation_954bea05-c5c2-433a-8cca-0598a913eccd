import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

export interface McpTool {
  name: string;
  description: string;
  inputSchema: any;
}

export interface McpToolCall {
  name: string;
  arguments: any;
}

export interface McpToolResult {
  content: any[];
  isError?: boolean;
}

export interface McpServerConfig {
  name: string;
  type: 'local' | 'remote';
  command?: string;
  args?: string[];
  env?: Record<string, string>;
  url?: string;
}

@Injectable()
export class McpService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(McpService.name);
  private mcpClients = new Map<string, Client>();
  private allTools: McpTool[] = [];

  constructor(private configService: ConfigService) {}

  public async onModuleInit() {
    const mcpConfig = this.configService.get('mcp');

    if (!mcpConfig.enabled) {
      this.logger.log('MCP is disabled, skipping initialization');
      return;
    }

    await this.initializeMcpServers(mcpConfig.servers);
    this.logger.log('MCP Service initialized successfully');
  }

  async onModuleDestroy() {
    await this.disconnectAllClients();
    this.logger.log('MCP Service destroyed');
  }

  private async initializeMcpServers(servers: McpServerConfig[]) {
    this.logger.log(`Initializing ${servers.length} MCP servers`);

    for (const serverConfig of servers) {
      try {
        await this.initializeMcpServer(serverConfig);
      } catch (error) {
        this.logger.error(`Failed to initialize MCP server ${serverConfig.name}: ${error.message}`, error.stack);
        // Continue with other servers even if one fails
      }
    }

    this.logger.log(`Successfully initialized ${this.mcpClients.size} MCP servers`);
  }

  private async initializeMcpServer(serverConfig: McpServerConfig) {
    this.logger.log(`Initializing MCP server: ${serverConfig.name} (${serverConfig.type})`);

    if (serverConfig.type === 'local') {
      await this.initializeLocalMcpServer(serverConfig);
    } else if (serverConfig.type === 'remote') {
      await this.initializeRemoteMcpServer(serverConfig);
    } else {
      throw new Error(`Unknown MCP server type: ${serverConfig.type}`);
    }
  }

  private async initializeLocalMcpServer(serverConfig: McpServerConfig) {
    if (!serverConfig.command) {
      throw new Error(`Local MCP server ${serverConfig.name} requires a command`);
    }

    this.logger.log(`Starting local MCP server: ${serverConfig.command} ${serverConfig.args?.join(' ') || ''}`);

    // Create transport and client using command/args approach
    const transport = new StdioClientTransport({
      command: serverConfig.command,
      args: serverConfig.args || [],
      env: { ...process.env, ...serverConfig.env },
    });

    const client = new Client({
      name: `sw-search-agent-${serverConfig.name}`,
      version: '1.0.0',
    }, {
      capabilities: {
        tools: {},
      },
    });

    // Connect to the server
    await client.connect(transport);
    this.mcpClients.set(serverConfig.name, client);

    this.logger.log(`MCP server ${serverConfig.name} connected successfully`);

    // Get available tools from this server
    const toolsResponse = await client.listTools();
    const serverTools = toolsResponse.tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema,
    }));

    this.allTools.push(...serverTools);
    this.logger.log(`Found ${serverTools.length} tools: ${serverTools.map(t => t.name).join(', ')}`);
  }

  private async initializeRemoteMcpServer(serverConfig: McpServerConfig) {
    // TODO: Implement remote MCP server connection
    // This would use WebSocket or HTTP transport
    throw new Error('Remote MCP servers not yet implemented');
  }

  async getAvailableTools(): Promise<McpTool[]> {
    this.logger.log(`Providing ${this.allTools.length} available MCP tools from ${this.mcpClients.size} servers`);
    this.logger.log(`Available tools: ${this.allTools.map(t => t.name).join(', ')}`);
    return this.allTools;
  }

  async executeTool(toolCall: McpToolCall): Promise<McpToolResult> {
    this.logger.log(`Executing tool: ${toolCall.name} with arguments: ${JSON.stringify(toolCall.arguments)}`);

    try {
      const startTime = Date.now();

      // Find which client has this tool
      const client = await this.findClientForTool(toolCall.name);
      if (!client) {
        this.logger.warn(`No MCP client found for tool: ${toolCall.name}`);
        return {
          content: [{ type: 'text', text: `Unknown tool: ${toolCall.name}` }],
          isError: true,
        };
      }

      // Execute the tool using the MCP client
      const result = await client.callTool({
        name: toolCall.name,
        arguments: toolCall.arguments,
      });

      const duration = Date.now() - startTime;
      this.logger.log(`Tool ${toolCall.name} executed in ${duration}ms. Success: ${!result.isError}`);

      return {
        content: Array.isArray(result.content) ? result.content : [result.content],
        isError: Boolean(result.isError),
      };
    } catch (error) {
      this.logger.error(`Error executing tool ${toolCall.name}: ${error.message}`, error.stack);
      return {
        content: [{ type: 'text', text: `Error: ${error.message}` }],
        isError: true,
      };
    }
  }

  private async findClientForTool(toolName: string): Promise<Client | null> {
    // For now, try each client until we find one that has the tool
    // In the future, we could maintain a mapping of tools to clients
    for (const [serverName, client] of this.mcpClients) {
      try {
        const toolsResponse = await client.listTools();
        const hasTool = toolsResponse.tools.some(tool => tool.name === toolName);
        if (hasTool) {
          this.logger.log(`Found tool ${toolName} in server ${serverName}`);
          return client;
        }
      } catch (error) {
        this.logger.warn(`Failed to list tools from server ${serverName}: ${error.message}`);
      }
    }
    return null;
  }

  private async disconnectAllClients() {
    this.logger.log('Disconnecting all MCP clients');

    for (const [serverName, client] of this.mcpClients) {
      try {
        await client.close();
        this.logger.log(`Disconnected from MCP server: ${serverName}`);
      } catch (error) {
        this.logger.warn(`Failed to disconnect from MCP server ${serverName}: ${error.message}`);
      }
    }

    this.mcpClients.clear();
  }



  // Method to refresh tools from all connected servers
  async refreshTools() {
    this.logger.log('Refreshing tools from all MCP servers');
    this.allTools = [];

    for (const [serverName, client] of this.mcpClients) {
      try {
        const toolsResponse = await client.listTools();
        const serverTools = toolsResponse.tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          inputSchema: tool.inputSchema,
        }));

        this.allTools.push(...serverTools);
        this.logger.log(`Refreshed ${serverTools.length} tools from server ${serverName}`);
      } catch (error) {
        this.logger.warn(`Failed to refresh tools from server ${serverName}: ${error.message}`);
      }
    }

    this.logger.log(`Total tools available: ${this.allTools.length}`);
  }
}
