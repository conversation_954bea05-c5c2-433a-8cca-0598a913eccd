import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  tool_call_id?: string;
  tool_calls?: any[];
}

@Injectable()
export class OpenaiService {
  private readonly logger = new Logger(OpenaiService.name);
  private openai: OpenAI;
  private model: string;

  constructor(private configService: ConfigService) {
    const config = this.configService.get('openai');

    this.logger.log(`Initializing OpenAI service with model: ${config.model}, baseURL: ${config.baseURL}`);

    this.openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
    });

    this.model = config.model;
    this.logger.log('OpenAI service initialized successfully');
  }

  async createChatCompletion(
    messages: ChatMessage[],
    tools?: any[],
    toolChoice?: any,
  ): Promise<OpenAI.Chat.Completions.ChatCompletion> {
    this.logger.log(`Creating chat completion with ${messages.length} messages, ${tools?.length || 0} tools`);

    const params: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
      model: this.model,
      messages: messages as OpenAI.Chat.Completions.ChatCompletionMessageParam[],
    };

    if (tools && tools.length > 0) {
      params.tools = tools;
      this.logger.log(`Tools available: ${tools.map(t => t.function.name).join(', ')}`);
      if (toolChoice) {
        params.tool_choice = toolChoice;
      }
    }

    try {
      const startTime = Date.now();
      const result = await this.openai.chat.completions.create(params);
      const duration = Date.now() - startTime;

      this.logger.log(`Chat completion successful in ${duration}ms. Usage: ${JSON.stringify(result.usage)}`);

      if (result.choices[0].message.tool_calls) {
        this.logger.log(`Assistant requested ${result.choices[0].message.tool_calls.length} tool calls`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Chat completion failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  async streamChatCompletion(
    messages: ChatMessage[],
    tools?: any[],
  ): Promise<AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>> {
    const params: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
      model: this.model,
      messages: messages as OpenAI.Chat.Completions.ChatCompletionMessageParam[],
      stream: true,
    };

    if (tools && tools.length > 0) {
      params.tools = tools;
    }

    return await this.openai.chat.completions.create(params);
  }
}
