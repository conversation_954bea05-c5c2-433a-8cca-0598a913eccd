import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from 'pg';

export interface DatabaseSchema {
  tables: TableInfo[];
  constraints: ConstraintInfo[];
  sampleData?: Record<string, any[]>;
}

export interface TableInfo {
  name: string;
  columns: ColumnInfo[];
  rowCount?: number;
}

export interface ColumnInfo {
  name: string;
  dataType: string;
  isNullable: boolean;
  defaultValue?: string;
  maxLength?: number;
  isPrimaryKey?: boolean;
  isForeignKey?: boolean;
  referencedTable?: string;
  referencedColumn?: string;
}

export interface ConstraintInfo {
  name: string;
  type: string;
  tableName: string;
  columnName: string;
  referencedTable?: string;
  referencedColumn?: string;
}

@Injectable()
export class ContextService implements OnModuleInit {
  private readonly logger = new Logger(ContextService.name);
  private pgClient: Client;
  private cachedSchema: DatabaseSchema | null = null;
  private lastSchemaUpdate: Date | null = null;
  private readonly CACHE_DURATION_MS = 5 * 60 * 1000; // 5 minutes

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    await this.initializeDatabase();
    this.logger.log('Context Service initialized successfully');
  }

  private async initializeDatabase() {
    const dbConfig = this.configService.get('database');
    
    this.pgClient = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.username,
      password: dbConfig.password,
      database: dbConfig.name,
    });

    try {
      await this.pgClient.connect();
      this.logger.log('Connected to PostgreSQL for context service');
    } catch (error) {
      this.logger.error(`Failed to connect to PostgreSQL: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSystemPromptWithContext(): Promise<string> {
    this.logger.log('Generating system prompt with database context');
    
    const contextConfig = this.configService.get('context');
    let systemPrompt = `
You are a helpful assistant that can search and analyze data from a PostgreSQL database.
You have access to database tools that allow you to:
- List all tables in the database
- Describe table schemas
- Execute read-only SQL queries

When a user asks a question, you should:
1. First understand what they're looking for
2. Use the available tools to explore the database structure if needed
3. Execute appropriate queries to find the information
4. Provide a clear, helpful response based on the data

Always be helpful and explain your reasoning when using database tools.`.trim();

    // if (contextConfig.includeSchema) {
    //   try {
    //     const schema = await this.getDatabaseSchema();
    //     systemPrompt += '\n\n' + this.formatSchemaForPrompt(schema);
    //   } catch (error) {
    //     this.logger.warn(`Failed to include schema in context: ${error.message}`);
    //   }
    // }

    return systemPrompt;
  }

  private async getDatabaseSchema(): Promise<DatabaseSchema> {
    // Check cache
    if (this.cachedSchema && this.lastSchemaUpdate) {
      const cacheAge = Date.now() - this.lastSchemaUpdate.getTime();
      if (cacheAge < this.CACHE_DURATION_MS) {
        this.logger.log('Using cached database schema');
        return this.cachedSchema;
      }
    }

    this.logger.log('Fetching fresh database schema');
    const startTime = Date.now();

    const contextConfig = this.configService.get('context');
    const schema: DatabaseSchema = {
      tables: await this.getTableInfo(contextConfig.maxTables),
      constraints: contextConfig.includeConstraints ? await this.getConstraintInfo() : [],
    };

    if (contextConfig.includeSampleData) {
      schema.sampleData = await this.getSampleData(schema.tables, contextConfig.sampleDataLimit);
    }

    this.cachedSchema = schema;
    this.lastSchemaUpdate = new Date();

    const duration = Date.now() - startTime;
    this.logger.log(`Database schema fetched in ${duration}ms. Tables: ${schema.tables.length}, Constraints: ${schema.constraints.length}`);

    return schema;
  }

  private async getTableInfo(maxTables: number): Promise<TableInfo[]> {
    const query = `
      SELECT 
        t.table_name,
        c.column_name,
        c.data_type,
        c.is_nullable,
        c.column_default,
        c.character_maximum_length,
        CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
        CASE WHEN fk.column_name IS NOT NULL THEN true ELSE false END as is_foreign_key,
        fk.referenced_table_name,
        fk.referenced_column_name
      FROM information_schema.tables t
      LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
      LEFT JOIN (
        SELECT ku.table_name, ku.column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        WHERE tc.constraint_type = 'PRIMARY KEY'
      ) pk ON c.table_name = pk.table_name AND c.column_name = pk.column_name
      LEFT JOIN (
        SELECT 
          ku.table_name, 
          ku.column_name,
          ccu.table_name as referenced_table_name,
          ccu.column_name as referenced_column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
        JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
      ) fk ON c.table_name = fk.table_name AND c.column_name = fk.column_name
      WHERE t.table_schema = 'public' AND t.table_type = 'BASE TABLE'
      ORDER BY t.table_name, c.ordinal_position
      LIMIT $1;
    `;

    const result = await this.pgClient.query(query, [maxTables * 50]); // Estimate columns per table
    
    const tablesMap = new Map<string, TableInfo>();
    
    for (const row of result.rows) {
      if (!tablesMap.has(row.table_name)) {
        tablesMap.set(row.table_name, {
          name: row.table_name,
          columns: [],
        });
      }
      
      const table = tablesMap.get(row.table_name)!;
      if (row.column_name) {
        table.columns.push({
          name: row.column_name,
          dataType: row.data_type,
          isNullable: row.is_nullable === 'YES',
          defaultValue: row.column_default,
          maxLength: row.character_maximum_length,
          isPrimaryKey: row.is_primary_key,
          isForeignKey: row.is_foreign_key,
          referencedTable: row.referenced_table_name,
          referencedColumn: row.referenced_column_name,
        });
      }
    }

    return Array.from(tablesMap.values()).slice(0, maxTables);
  }

  private async getConstraintInfo(): Promise<ConstraintInfo[]> {
    const query = `
      SELECT 
        tc.constraint_name,
        tc.constraint_type,
        tc.table_name,
        ku.column_name,
        ccu.table_name as referenced_table_name,
        ccu.column_name as referenced_column_name
      FROM information_schema.table_constraints tc
      LEFT JOIN information_schema.key_column_usage ku ON tc.constraint_name = ku.constraint_name
      LEFT JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
      WHERE tc.table_schema = 'public'
      ORDER BY tc.table_name, tc.constraint_name;
    `;

    const result = await this.pgClient.query(query);
    
    return result.rows.map(row => ({
      name: row.constraint_name,
      type: row.constraint_type,
      tableName: row.table_name,
      columnName: row.column_name,
      referencedTable: row.referenced_table_name,
      referencedColumn: row.referenced_column_name,
    }));
  }

  private async getSampleData(tables: TableInfo[], limit: number): Promise<Record<string, any[]>> {
    const sampleData: Record<string, any[]> = {};
    
    for (const table of tables.slice(0, 10)) { // Limit to first 10 tables for performance
      try {
        const query = `SELECT * FROM "${table.name}" LIMIT $1`;
        const result = await this.pgClient.query(query, [limit]);
        sampleData[table.name] = result.rows;
      } catch (error) {
        this.logger.warn(`Failed to get sample data for table ${table.name}: ${error.message}`);
      }
    }
    
    return sampleData;
  }

  private formatSchemaForPrompt(schema: DatabaseSchema): string {
    let prompt = '\n## DATABASE SCHEMA INFORMATION\n\n';
    
    prompt += `The database contains ${schema.tables.length} tables:\n\n`;
    
    for (const table of schema.tables) {
      prompt += `### Table: ${table.name}\n`;
      prompt += 'Columns:\n';
      
      for (const column of table.columns) {
        let columnDesc = `- ${column.name}: ${column.dataType}`;
        if (column.maxLength) columnDesc += `(${column.maxLength})`;
        if (!column.isNullable) columnDesc += ' NOT NULL';
        if (column.isPrimaryKey) columnDesc += ' PRIMARY KEY';
        if (column.isForeignKey) columnDesc += ` REFERENCES ${column.referencedTable}(${column.referencedColumn})`;
        if (column.defaultValue) columnDesc += ` DEFAULT ${column.defaultValue}`;
        
        prompt += columnDesc + '\n';
      }
      
      prompt += '\n';
    }
    
    if (schema.constraints.length > 0) {
      prompt += '### Key Relationships:\n';
      const foreignKeys = schema.constraints.filter(c => c.type === 'FOREIGN KEY');
      for (const fk of foreignKeys) {
        prompt += `- ${fk.tableName}.${fk.columnName} → ${fk.referencedTable}.${fk.referencedColumn}\n`;
      }
      prompt += '\n';
    }
    
    if (schema.sampleData) {
      prompt += '### Sample Data (first few rows):\n';
      for (const [tableName, rows] of Object.entries(schema.sampleData)) {
        if (rows.length > 0) {
          prompt += `${tableName}: ${rows.length} sample rows available\n`;
        }
      }
      prompt += '\n';
    }
    
    prompt += 'Use this schema information to write accurate SQL queries and provide helpful responses about the database structure.';
    
    return prompt;
  }

  async onModuleDestroy() {
    if (this.pgClient) {
      await this.pgClient.end();
      this.logger.log('Disconnected from PostgreSQL database');
    }
  }
}
