import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ContextService {
  private readonly logger = new Logger(ContextService.name);

  constructor(private configService: ConfigService) {
    this.logger.log('Context Service initialized successfully');
  }

  getSystemPromptWithContext(): string {
    this.logger.log('Generating system prompt with database context');

    const contextConfig = this.configService.get('context');

    let systemPrompt = `You are a helpful database assistant. Follow these steps precisely:

1. Read the user's request carefully.
2. To fetch data, call **exactly one** query tool:
   • **Only** \`SELECT …\` statements.
   • **Never** \`INSERT\`, \`UPDATE\`, \`DELETE\`, \`CREATE\`, \`DROP\`, or \`ALTER\`.
   • Add a reasonable \`LIMIT\` (≤ 50) unless the user explicitly asks for all rows.
3. Wait for the tool result.
4. Compose a concise, helpful answer for the user:
   • Explain in plain language what you did.
   • Summarize or tabulate key rows; do not dump large raw JSON.
   • If no rows are returned, say so and suggest what data might be missing.

Always use the OpenAI **tool-calling interface** (no custom tags or wrappers).
If a question can be answered without the database, respond directly.

When citing table or column names, keep their exact PostgreSQL casing.

(You are connected to a production replica; treat the data with care.)`;

    if (contextConfig.includeStaticDescription) {
      systemPrompt += '\n\n' + this.getStaticDatabaseDescription();
    }

    return systemPrompt;
  }

  private getStaticDatabaseDescription(): string {
    const contextConfig = this.configService.get('context');
    const description = contextConfig.staticDescription || '';

    if (description) {
      return `## DATABASE STRUCTURE OVERVIEW\n\n${description}`;
    }

    // Default fallback description - should be configured via environment
    return `## DATABASE STRUCTURE OVERVIEW

This database contains various tables for data analysis. Use the available query tools to explore the structure and data.
Configure CONTEXT_STATIC_DESCRIPTION environment variable to provide specific database schema information.`;
  }
}
